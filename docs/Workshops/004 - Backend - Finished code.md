Okay, PM. As requested, here is the code for the backend pieces we discussed and partially implemented during the pair-coding session for user story **E2.S1 - Create and Auto-Select New Chat Session**, plus some related infrastructure setup from **E7.S3** and **E7.S4** that enables it.

I've included full KDoc documentation for clarity. This is how these files would look after implementing the backend parts of E2.S1 and getting the basic E7 infrastructure in place.

---

**1. Data Models & DTOs (`app/shared/models/` and `app/api/server/`)**

These are the data structures passed between layers or used for API communication.

```kotlin
// app/shared/models/ChatSession.kt
package com.your_app_package.app.shared.models

import kotlinx.serialization.Serializable
import java.util.UUID

/**
 * Represents a full chat session including its messages.
 * This is typically loaded when a session is selected for viewing.
 * Used as a data model passed between Service and UI layers, and as an API DTO.
 */
@Serializable // Make it serializable for Ktor JSON
data class ChatSession(
    val id: UUID,
    val name: String,
    val createdAt: Long, // Unix timestamp
    val updatedAt: Long, // Unix timestamp
    val groupId: UUID?, // Nullable UUID reference for grouping (V1 uses UUID based on DB schema)
    val currentModelId: UUID?, // ID of the model selected for this session
    val currentSettingsId: UUID?, // ID of the settings selected for this session
    val messages: List<ChatMessage> = emptyList() // Messages for this session
)

/**
 * Represents a summary of a chat session, used for listing sessions in the UI.
 * Contains less detail than the full ChatSession.
 * Used as a data model passed between Service and UI layers, and as an API DTO.
 */
@Serializable // Make it serializable for Ktor JSON
data class ChatSessionSummary(
    val id: UUID,
    val name: String,
    val createdAt: Long,
    val updatedAt: Long,
    val groupId: UUID?, // Nullable UUID reference for grouping
    // groupName might be added later if group details are loaded/joined
)


// app/shared/models/ChatMessage.kt (Needed for ChatSession)
package com.your_app_package.app.shared.models

import kotlinx.serialization.Serializable
import java.util.UUID

/**
 * Represents a single message within a chat session.
 * Used as a data model passed between Service and UI layers, and as an API DTO.
 */
@Serializable // Make it serializable for Ktor JSON
data class ChatMessage(
    val id: UUID,
    val sessionId: UUID,
    val role: String, // e.g., "user", "assistant", "system" (though system often in settings)
    val content: String,
    val createdAt: Long, // Unix timestamp
    val updatedAt: Long, // Unix timestamp
    val sequence: Int, // Order within the session
    val modelId: UUID?, // Model used for this message (mainly assistant)
    val settingsId: UUID?, // Settings used for this message (mainly assistant)
    // Add V1 fields: isEdited, isDeleted if needed in the model itself
)

// app/api/server/CreateSessionRequest.kt (Specific DTO for the API endpoint request body)
package com.your_app_package.app.api.server

import kotlinx.serialization.Serializable

/**
 * Data Transfer Object for the request body of the POST /api/v1/sessions endpoint (E2.S1).
 * Contains the optional name for the new session.
 */
@Serializable
data class CreateSessionRequest(
    val name: String?
)
```

**2. Database Schema Definitions (`app/data/models/`)**

These define the tables using the Exposed DSL.

```kotlin
// app/data/models/ChatSessions.kt
package com.your_app_package.app.data.models

import org.jetbrains.exposed.dao.id.UUIDTable
import org.jetbrains.exposed.sql.ReferenceOption
import org.jetbrains.exposed.sql.kotlin.datetime.CurrentTimestamp
import org.jetbrains.exposed.sql.kotlin.datetime.timestamp

/**
 * Exposed table definition for chat sessions.
 * Implements E7.S4 schema requirements for ChatSession table.
 */
object ChatSessions : UUIDTable("chat_sessions") {
    val name = text("name").default("New Session") // Default name
    val createdAt = long("created_at").clientDefault { System.currentTimeMillis() } // Use long for Unix timestamp
    val updatedAt = long("updated_at").clientDefault { System.currentTimeMillis() }

    // V1 Grouping: Using UUID reference, matching schema discussion
    // If using String group name directly, change column type to text("group_name").nullable()
    // Sticking to UUID as planned in refined schema, assuming a 'groups' concept or self-reference later.
    val groupId = uuid("group_id").nullable().index() // Can reference another session.id or a groups.id

    // Foreign keys linking to models/settings used by the session (V1 adds these)
    val currentModelId = uuid("current_model_id").nullable().references(LLMModels.id, onDelete = ReferenceOption.SET_NULL).index()
    val currentSettingsId = uuid("current_settings_id").nullable().references(ModelSettings.id, onDelete = ReferenceOption.SET_NULL).index()

    // UUIDTable provides the 'id' PrimaryKey automatically.
}

// app/data/models/ChatMessages.kt (Needed by ChatSessions for foreign key)
package com.your_app_package.app.data.models

import org.jetbrains.exposed.dao.id.UUIDTable
import org.jetbrains.exposed.sql.ReferenceOption
import org.jetbrains.exposed.sql.index

/**
 * Exposed table definition for chat messages.
 * Implements E7.S4 schema requirements for ChatMessage table.
 */
object ChatMessages : UUIDTable("chat_messages") {
    // Foreign key to ChatSessions, crucial for CASCADE DELETE (E2.S6)
    val sessionId = uuid("session_id").references(ChatSessions.id, onDelete = ReferenceOption.CASCADE).index()
    val role = text("role") // "user", "assistant"
    val content = text("content")
    val createdAt = long("created_at").clientDefault { System.currentTimeMillis() } // Use long for Unix timestamp
    val updatedAt = long("updated_at").clientDefault { System.currentTimeMillis() }
    val sequence = integer("sequence").index() // Order within session (E1.S5)

    // Foreign keys linking to model/settings used for THIS message (V1 adds these)
    val modelId = uuid("model_id").nullable().references(LLMModels.id, onDelete = ReferenceOption.SET_NULL).index()
    val settingsId = uuid("settings_id").nullable().references(ModelSettings.id, onDelete = ReferenceOption.SET_NULL).index()
}

// app/data/models/LLMModels.kt (Needed by ChatSessions and ChatMessages)
package com.your_app_package.app.data.models

import org.jetbrains.exposed.dao.id.UUIDTable
import org.jetbrains.exposed.sql.javatime.CurrentTimestamp
import org.jetbrains.exposed.sql.javatime.timestamp
import org.jetbrains.exposed.sql.ReferenceOption

/**
 * Exposed table definition for LLM model configurations.
 * Implements E7.S4 schema requirements for LLMModel table.
 */
object LLMModels : UUIDTable("llm_models") {
    val name = text("name").uniqueIndex()
    val baseUrl = text("base_url")
    // Stores the ID/alias used to retrieve the API key from the OS credential manager (E5.S1)
    val apiKeyId = text("api_key_id").nullable()
    val type = text("type") // e.g., "openai", "openrouter"

    // Optional: link to a default settings profile for this model (V1 adds this)
    val defaultSettingsId = uuid("default_settings_id").nullable().references(ModelSettings.id, onDelete = ReferenceOption.SET_NULL).index()
}

// app/data/models/ModelSettings.kt (Needed by ChatSessions and ChatMessages, referenced by LLMModels)
package com.your_app_package.app.data.models

import org.jetbrains.exposed.dao.id.UUIDTable
import org.jetbrains.exposed.sql.ReferenceOption
import org.jetbrains.exposed.sql.index

/**
 * Exposed table definition for model settings configurations.
 * Implements E7.S4 schema requirements for ModelSettings table.
 */
object ModelSettings : UUIDTable("model_settings") {
    // Foreign key linking settings back to the model they belong to (E4.S5)
    val modelId = uuid("model_id").references(LLMModels.id, onDelete = ReferenceOption.CASCADE).index()
    val name = text("name") // e.g., "Default", "Creative"
    val systemMessage = text("system_message").nullable() // E4.S6
    val temperature = real("temperature").nullable() // E4.S6
    val maxTokens = integer("max_tokens").nullable() // E4.S6
    val customParamsJson = text("custom_params_json").nullable() // Store arbitrary JSON for other params (E4.S6)
}
```

**3. Data Access Object (DAO) Interface and Exposed Implementation (`app/data/dao/` and `app/data/sqlite/`)**

Defines the contract for session data access and its Exposed implementation.

```kotlin
// app/data/dao/SessionDao.kt
package com.your_app_package.app.data.dao

import com.your_app_package.app.shared.models.ChatSession
import com.your_app_package.app.shared.models.ChatSessionSummary
import java.util.UUID

/**
 * Data Access Object interface for ChatSession entities.
 * Defines the contract for interacting with session data persistence.
 * Consumed by the Application/Service layer.
 */
interface SessionDao {
    /**
     * Retrieves a summary of all chat sessions.
     * Used for displaying the session list (E2.S3).
     * @return List of ChatSessionSummary objects, ordered e.g., by last updated.
     */
    fun getAllSessions(): List<ChatSessionSummary>

    /**
     * Retrieves the full details of a specific chat session.
     * Used for loading a session's history (E2.S4).
     * Messages are typically included in the returned object.
     * @param id The UUID of the session to retrieve.
     * @return The ChatSession object, or null if not found.
     */
    fun getSessionById(id: UUID): ChatSession?

    /**
     * Inserts a new chat session record into the database.
     * Used when creating a new session (E2.S1 backend).
     * Timestamps and ID are handled by the database/Exposed.
     * @param session A ChatSession object containing the initial data (e.g., name).
     * @return The fully created ChatSession object including generated ID and timestamps.
     * @throws Exception if insertion fails.
     */
    fun insertSession(session: ChatSession): ChatSession // Updated return type during pair coding

    /**
     * Updates fields of an existing chat session.
     * Used for renaming (E2.S5), changing group (E6.S1), or updating model/settings (E4.S7 backend).
     * @param id The UUID of the session to update.
     * @param name Optional new name for the session.
     * @param groupId Optional new group ID for the session.
     * @param currentModelId Optional new model ID for the session.
     * @param currentSettingsId Optional new settings ID for the session.
     * @throws Exception if update fails or session not found.
     */
    fun updateSession(id: UUID, name: String? = null, groupId: UUID? = null, currentModelId: UUID? = null, currentSettingsId: UUID? = null) // TODO: Add logic to update 'updatedAt'

    /**
     * Deletes a specific chat session.
     * Used for deleting sessions (E2.S6 backend).
     * Associated messages should be deleted automatically via CASCADE.
     * @param id The UUID of the session to delete.
     * @throws Exception if deletion fails or session not found.
     */
    fun deleteSession(id: UUID)
}
```

```kotlin
// app/data/sqlite/SessionDaoExposed.kt
package com.your_app_package.app.data.sqlite

import com.your_app_package.app.data.dao.SessionDao // Implements interface
import com.your_app_package.app.data.models.ChatSessions // Uses Exposed table definition
import com.your_app_package.app.shared.models.ChatSession // Uses shared data models
import com.your_app_package.app.shared.models.ChatSessionSummary
import org.jetbrains.exposed.sql.*
import org.jetbrains.exposed.sql.transactions.transaction // For DB transactions
import kotlinx.coroutines.Dispatchers // For IO dispatcher
import java.util.UUID

/**
 * Exposed implementation of the SessionDao interface.
 * Handles interaction with the SQLite database for ChatSession entities.
 */
class SessionDaoExposed : SessionDao {

    /**
     * @inheritDoc
     */
    override fun getAllSessions(): List<ChatSessionSummary> = transaction(Dispatchers.IO) {
        // Implementation for E2.S3 backend part
        ChatSessions.selectAll()
            .orderBy(ChatSessions.updatedAt, Order.DESC) // Order by most recently updated
            .map { toChatSessionSummary(it) } // Map result rows to summary models
    }

    /**
     * @inheritDoc
     */
    override fun getSessionById(id: UUID): ChatSession? = transaction(Dispatchers.IO) {
        // Implementation for E2.S4 backend part
        ChatSessions.select { ChatSessions.id eq id }
            .limit(1)
            .map { toChatSession(it) } // Map result row to full model
            .firstOrNull() // Get the single result or null
    }

    /**
     * @inheritDoc
     * Inserts a new session and returns the full created session object.
     */
    override fun insertSession(session: ChatSession): ChatSession = transaction(Dispatchers.IO) {
        // Exposed handles generating the UUID and setting clientDefault timestamps
        val insertStatement = ChatSessions.insert {
            it[name] = session.name ?: "New Session" // Use provided name or table default
            // createdAt and updatedAt are clientDefault in table definition
            // groupId, currentModelId, currentSettingsId are nullable defaults
        }

        // Retrieve the full inserted row data using the generated ID
        val insertedId = insertStatement get ChatSessions.id
        val insertedRow = ChatSessions.select { ChatSessions.id eq insertedId }.singleOrNull()
             ?: throw IllegalStateException("Failed to retrieve inserted session with ID: $insertedId") // Should not happen if insert succeeded

        toChatSession(insertedRow) // Map the result row to our shared model
    }

    /**
     * @inheritDoc
     */
    override fun updateSession(id: UUID, name: String?, groupId: UUID?, currentModelId: UUID?, currentSettingsId: UUID?) = transaction(Dispatchers.IO) {
        // Implementation for E2.S5, E4.S7, E6.S1 backend part
        val updateCount = ChatSessions.update({ ChatSessions.id eq id }) {
            if (name != null) it[ChatSessions.name] = name
            if (groupId != null) it[ChatSessions.groupId] = groupId // Handle null assignment if needed
            if (currentModelId != null) it[ChatSessions.currentModelId] = currentModelId
            if (currentSettingsId != null) it[ChatSessions.currentSettingsId] = currentSettingsId
            it[ChatSessions.updatedAt] = System.currentTimeMillis() // Always update timestamp on modification
        }
        if (updateCount == 0) {
            // Optional: Log or throw exception if session not found
            println("Warning: Session with ID $id not found for update.")
        }
    }

    /**
     * @inheritDoc
     */
    override fun deleteSession(id: UUID) = transaction(Dispatchers.IO) {
        // Implementation for E2.S6 backend part
        val deleteCount = ChatSessions.deleteWhere { ChatSessions.id eq id }
        if (deleteCount == 0) {
             // Optional: Log or throw exception if session not found
             println("Warning: Session with ID $id not found for deletion.")
        }
        // ON DELETE CASCADE in table definition handles messages
    }

    /**
     * Maps an Exposed ResultRow from the ChatSessions table to a ChatSession shared model.
     * Note: Does NOT load associated messages here. Messages are loaded separately (E2.S4).
     * @param row The ResultRow from an Exposed query.
     * @return The mapped ChatSession object.
     */
    private fun toChatSession(row: ResultRow) = ChatSession(
        id = row[ChatSessions.id].value,
        name = row[ChatSessions.name],
        createdAt = row[ChatSessions.createdAt],
        updatedAt = row[ChatSessions.updatedAt],
        groupId = row[ChatSessions.groupId]?.value,
        currentModelId = row[ChatSessions.currentModelId]?.value,
        currentSettingsId = row[ChatSessions.currentSettingsId]?.value,
        messages = emptyList() // Messages are NOT loaded by this DAO method
    )

     /**
     * Maps an Exposed ResultRow from the ChatSessions table to a ChatSessionSummary shared model.
     * Used for the session list.
     * @param row The ResultRow from an Exposed query.
     * @return The mapped ChatSessionSummary object.
     */
    private fun toChatSessionSummary(row: ResultRow) = ChatSessionSummary(
        id = row[ChatSessions.id].value,
        name = row[ChatSessions.name],
        createdAt = row[ChatSessions.createdAt],
        updatedAt = row[ChatSessions.updatedAt],
        groupId = row[ChatSessions.groupId]?.value
    )
}
```

**4. Application/Service Layer Interface and Implementation (`app/service/`)**

Defines the business logic contract for chat operations and its implementation.

```kotlin
// app/service/ChatService.kt
package com.your_app_package.app.service

import com.your_app_package.app.shared.models.* // Uses shared models
import java.util.UUID

/**
 * Interface for the Chat Service.
 * Defines the business logic operations related to chat sessions and messages.
 * Consumed by the Embedded Ktor Server (ApiRoutes).
 */
interface ChatService {
    /**
     * Retrieves summaries of all chat sessions.
     * Called by the Ktor route handler for GET /api/v1/sessions (E2.S3 backend).
     * @return A list of chat session summaries.
     */
    fun getAllSessionsSummaries(): List<ChatSessionSummary>

    /**
     * Creates a new chat session.
     * Called by the Ktor route handler for POST /api/v1/sessions (E2.S1 backend).
     * @param name An optional initial name for the session.
     * @return The newly created ChatSession object.
     * @throws Exception if creation fails.
     */
    fun createSession(name: String?): ChatSession

    /**
     * Retrieves the full details of a chat session, including its messages.
     * Called by the Ktor route handler for GET /api/v1/sessions/{id} (E2.S4 backend).
     * Messages are loaded and included in the returned ChatSession object.
     * @param id The UUID of the session.
     * @return The ChatSession object, or null if not found.
     */
    fun getSessionDetails(id: UUID): ChatSession? // TODO: Implementation needs to also load messages

    /**
     * Updates details of a chat session.
     * Called by the Ktor route handler for PUT /api/v1/sessions/{id} (E2.S5, E4.S7 backend).
     * @param id The UUID of the session to update.
     * @param name Optional new name.
     * @param modelId Optional new current model ID.
     * @param settingsId Optional new current settings ID.
     * @return The updated ChatSession object.
     * @throws Exception if update fails or session not found.
     */
    fun updateSessionDetails(id: UUID, name: String? = null, modelId: UUID? = null, settingsId: UUID? = null): ChatSession // TODO: Implementation

     /**
     * Deletes a specific chat session.
     * Called by the Ktor route handler for DELETE /api/v1/sessions/{id} (E2.S6 backend).
     * @param id The UUID of the session to delete.
     * @throws Exception if deletion fails or session not found.
     */
    fun deleteSession(id: UUID) // TODO: Implementation

     /**
     * Assigns a session to a group.
     * Called by the Ktor route handler for PUT /api/v1/sessions/{id}/group (E6.S1 backend).
     * @param id The UUID of the session.
     * @param groupId The UUID of the group, or null to remove from group.
     * @return Summary of the updated session.
     * @throws Exception if update fails or session not found.
     */
    fun assignSessionToGroup(id: UUID, groupId: UUID?): ChatSessionSummary // TODO: Implementation

    /**
     * Processes a new user message: saves it, calls the LLM, saves the assistant response.
     * Called by the Ktor route handler for POST /api/v1/sessions/{sessionId}/messages (E1.S4 backend).
     * @param sessionId The UUID of the session the message belongs to.
     * @param content The text content of the user message.
     * @return A list containing the saved user message and the generated assistant message.
     * @throws Exception if processing or LLM call fails.
     */
    suspend fun processNewMessage(sessionId: UUID, content: String): List<ChatMessage> // TODO: Implementation (Big one - E1.S4)

    /**
     * Updates the content of a specific message.
     * Called by the Ktor route handler for PUT /api/v1/messages/{id} (E3.S3 backend).
     * @param id The UUID of the message.
     * @param content The new text content.
     * @return The updated ChatMessage object.
     * @throws Exception if update fails or message not found.
     */
    fun updateMessageContent(id: UUID, content: String): ChatMessage // TODO: Implementation

    /**
     * Deletes a specific message.
     * Called by the Ktor route handler for DELETE /api/v1/messages/{id} (E3.S4 backend).
     * @param id The UUID of the message to delete.
     * @throws Exception if deletion fails or message not found.
     */
    fun deleteMessage(id: UUID) // TODO: Implementation
}
```

```kotlin
// app/service/ChatServiceImpl.kt
package com.your_app_package.app.service

import com.your_app_package.app.data.dao.MessageDao // Dependency
import com.your_app_package.app.data.dao.ModelDao // Dependency
import com.your_app_package.app.data.dao.SessionDao // Dependency (Used in E2.S1)
import com.your_app_package.app.data.dao.SettingsDao // Dependency
import com.your_app_package.app.external.llm.LLMApiClient // Dependency
import com.your_app_package.app.shared.models.* // Uses shared models
import java.util.UUID

/**
 * Implementation of the Chat Service interface.
 * Contains the core business logic for chat operations.
 * Depends on DAOs and External Service clients via constructor injection.
 */
class ChatServiceImpl(
    private val sessionDao: SessionDao, // Injected Session DAO
    private val messageDao: MessageDao, // TODO: Needs implementation
    private val modelDao: ModelDao, // TODO: Needs implementation
    private val settingsDao: SettingsDao, // TODO: Needs implementation
    private val llmApiClient: LLMApiClient // TODO: Needs implementation
) : ChatService {

    /**
     * @inheritDoc
     * Retrieves a summary of all chat sessions from the DAO.
     * Implements E2.S3 backend logic.
     */
    override fun getAllSessionsSummaries(): List<ChatSessionSummary> {
        return sessionDao.getAllSessions()
    }

    /**
     * @inheritDoc
     * Creates a new chat session by calling the Session DAO.
     * Implements E2.S1 backend logic.
     */
    override fun createSession(name: String?): ChatSession {
        // Create a temporary ChatSession object with the data we have
        // The DAO handles generating ID, timestamps, and applying defaults
        val tempSessionForInsertion = ChatSession(
            id = UUID.randomUUID(), // Placeholder ID, DAO will generate/use its own
            name = name ?: "", // Provide the name or empty; DAO default handles "New Session"
            createdAt = 0, // Placeholder, DAO sets
            updatedAt = 0, // Placeholder, DAO sets
            groupId = null,
            currentModelId = null,
            currentSettingsId = null,
            messages = emptyList() // No messages yet
        )
        // Call the DAO to insert and return the fully formed ChatSession
        return sessionDao.insertSession(tempSessionForInsertion)
    }

    /**
     * @inheritDoc
     * Retrieves a full session including messages.
     * Implements E2.S4 backend logic.
     */
    override fun getSessionDetails(id: UUID): ChatSession? {
        // Get the basic session details first
        val session = sessionDao.getSessionById(id) ?: return null

        // Now get the messages for this session
        val messages = messageDao.getMessagesBySessionId(id) // Assumes DAO orders messages correctly

        // Return the session with messages included
        return session.copy(messages = messages) // Create a copy with messages added
    }

    /**
     * @inheritDoc
     */
    override fun updateSessionDetails(id: UUID, name: String?, modelId: UUID?, settingsId: UUID?): ChatSession {
        // Implements E2.S5, E4.S7 backend logic
        sessionDao.updateSession(id, name, currentModelId = modelId, currentSettingsId = settingsId)
        // After update, fetch the updated session to return it
        return sessionDao.getSessionById(id) ?: throw IllegalStateException("Updated session not found!") // Should not happen
    }

     /**
     * @inheritDoc
     */
    override fun deleteSession(id: UUID) {
        // Implements E2.S6 backend logic
        // DAO handles cascading delete of messages
        sessionDao.deleteSession(id)
    }

     /**
     * @inheritDoc
     */
    override fun assignSessionToGroup(id: UUID, groupId: UUID?): ChatSessionSummary {
        // Implements E6.S1 backend logic
        sessionDao.updateSession(id, groupId = groupId)
        // After update, fetch the updated session summary to return it
        // Need to get the summary from DAO, potentially joining for group name later
        // For V1, just return a basic summary with the updated groupId
         val updatedSession = sessionDao.getSessionById(id) ?: throw IllegalStateException("Assigned session not found!")
         return ChatSessionSummary(
            id = updatedSession.id,
            name = updatedSession.name,
            createdAt = updatedSession.createdAt,
            updatedAt = updatedSession.updatedAt,
            groupId = updatedSession.groupId
         )
    }


    /**
     * @inheritDoc
     */
    override suspend fun processNewMessage(sessionId: UUID, content: String): List<ChatMessage> {
         // TODO: Implement E1.S4 backend logic (fetch history, get model/settings/key, call LLM, save assistant)
         TODO("Not yet implemented for E1.S4") // Placeholder
    }

    /**
     * @inheritDoc
     */
    override fun updateMessageContent(id: UUID, content: String): ChatMessage {
        // TODO: Implement E3.S3 backend logic
        messageDao.updateMessage(id, content)
        // Need to return the updated message - MessageDao needs a getById?
        // Or updateMessage DAO returns the updated message? Let's add a getById to MessageDao.
        // return messageDao.getMessageById(id) ?: throw IllegalStateException("Updated message not found!")
        TODO("Needs MessageDao.getMessageById and implementation")
    }

    /**
     * @inheritDoc
     */
    override fun deleteMessage(id: UUID) {
        // TODO: Implement E3.S4 backend logic
        messageDao.deleteMessage(id)
    }
}

// app/service/ModelService.kt (Needed as a dependency in ApiRoutes, even if empty for now)
package com.your_app_package.app.service

import com.your_app_package.app.shared.models.LLMModel
import com.your_app_package.app.shared.models.ModelSettings
import java.util.UUID

/**
 * Interface for the Model Service.
 * Defines business logic for LLM models, settings, and API key handling.
 * Consumed by the Embedded Ktor Server (ApiRoutes).
 */
interface ModelService {
    // --- Models ---
    fun getAllModels(): List<LLMModel> // TODO: Implementation (E4.S2)
    fun addModel(name: String, baseUrl: String, type: String, apiKey: String?): LLMModel // TODO: Implementation (E4.S1)
    fun updateModel(id: UUID, name: String? = null, baseUrl: String? = null, type: String? = null, apiKey: String? = null): LLMModel // TODO: Implementation (E4.S3)
    fun deleteModel(id: UUID) // TODO: Implementation (E4.S4)
    fun isApiKeyConfiguredForModel(modelId: UUID): Boolean // TODO: Implementation (E5.S4)

    // --- Settings ---
    fun getSettingsById(id: UUID): ModelSettings? // TODO: Implementation (E4.S6)
    fun getAllSettings(): List<ModelSettings> // TODO: Implementation (Utility)
    fun addSettings(modelId: UUID, name: String, systemMessage: String?, temperature: Float?, maxTokens: Int?, customParamsJson: String?): ModelSettings // TODO: Implementation (E4.S5)
    fun updateSettings(id: UUID, name: String?, systemMessage: String?, temperature: Float?, maxTokens: Int?, customParamsJson: String?): ModelSettings // TODO: Implementation (E4.S6)
    fun deleteSettings(id: UUID) // TODO: Implementation (E4.S5)
}

// app/service/ModelServiceImpl.kt (Placeholder implementation)
package com.your_app_package.app.service

import com.your_app_package.app.data.dao.ModelDao
import com.your_app_package.app.data.dao.SettingsDao
import com.your_app_package.app.external.security.CredentialManager
import com.your_app_package.app.shared.models.LLMModel
import com.your_app_package.app.shared.models.ModelSettings
import java.util.UUID

/**
 * Placeholder implementation of the Model Service.
 * Requires DAOs and CredentialManager to be injected.
 * Most methods are TODOs for later sprints.
 */
class ModelServiceImpl(
    private val modelDao: ModelDao, // TODO: Needs implementation
    private val settingsDao: SettingsDao, // TODO: Needs implementation
    private val credentialManager: CredentialManager // TODO: Needs implementation (E5.S1)
) : ModelService {
    override fun getAllModels(): List<LLMModel> { TODO("Not yet implemented for E4.S2") }
    override fun addModel(name: String, baseUrl: String, type: String, apiKey: String?): LLMModel { TODO("Not yet implemented for E4.S1") }
    override fun updateModel(id: UUID, name: String?, baseUrl: String?, type: String?, apiKey: String?): LLMModel { TODO("Not yet implemented for E4.S3") }
    override fun deleteModel(id: UUID) { TODO("Not yet implemented for E4.S4") }
    override fun isApiKeyConfiguredForModel(modelId: UUID): Boolean { TODO("Not yet implemented for E5.S4") }

    override fun getSettingsById(id: UUID): ModelSettings? { TODO("Not yet implemented for E4.S6") }
    override fun getAllSettings(): List<ModelSettings> { TODO("Not yet implemented for Utility") }
    override fun getSettingsByModelId(modelId: UUID): List<ModelSettings> { TODO("Not yet implemented - Internal/Maybe exposed") }
    override fun addSettings(modelId: UUID, name: String, systemMessage: String?, temperature: Float?, maxTokens: Int?, customParamsJson: String?): ModelSettings { TODO("Not yet implemented for E4.S5") }
    override fun updateSettings(id: UUID, name: String?, systemMessage: String?, temperature: Float?, maxTokens: Int?, customParamsJson: String?): ModelSettings { TODO("Not yet implemented for E4.S6") }
    override fun deleteSettings(id: UUID) { TODO("Not yet implemented for E4.S5") }
}
```

**5. Embedded Ktor Server Routes (`app/api/server/`)**

Defines the HTTP endpoints and calls the Service layer.

```kotlin
// app/api/server/ApiRoutes.kt
package com.your_app_package.app.api.server

import io.ktor.server.routing.*
import io.ktor.server.application.*
import io.ktor.server.request.*
import io.ktor.server.response.*
import io.ktor.http.*
import com.your_app_package.app.service.ChatService // Dependency
import com.your_app_package.app.service.ModelService // Dependency
import com.your_app_package.app.shared.models.ChatSession // DTOs for response
import java.util.UUID // Using UUIDs

/**
 * Defines the API routes for the embedded Ktor server.
 * Each route handler calls the appropriate method on the injected Service layer interfaces.
 * @param chatService The injected ChatService implementation.
 * @param modelService The injected ModelService implementation.
 */
fun Route.apiRoutes(chatService: ChatService, modelService: ModelService) {
    // Top-level route for the API version
    route("/api/v1") {
        /**
         * GET /api/v1/status
         * Simple health check endpoint (E7.S3).
         * @return 200 OK with plain text "OK".
         */
        get("/status") {
            call.respondText("OK")
        }

        /**
         * POST /api/v1/sessions
         * Creates a new chat session (E2.S1 backend).
         * Request body can optionally contain a "name" field.
         * @body CreateSessionRequest Optional JSON object with a "name" string.
         * @return 201 Created with the created ChatSession object in the body.
         * @throws BadRequestException if request body is malformed.
         * @throws Exception if session creation fails in the service layer.
         */
        post("/sessions") {
            val request = try {
                // Use receiveNullable in case the request body is empty or missing name
                call.receiveNullable<CreateSessionRequest>()
            } catch (e: Exception) {
                // Catch serialization/deserialization errors
                application.log.error("Failed to receive CreateSessionRequest body", e) // Log error
                call.respond(HttpStatusCode.BadRequest, "Invalid request body format: ${e.message}")
                return@post
            }

            try {
                // Call the Chat Service to create the session
                val newSession = chatService.createSession(request?.name)
                // Respond with the created session object and 201 status
                call.respond(HttpStatusCode.Created, newSession)
            } catch (e: Exception) {
                // Catch any exceptions from the service layer (e.g., DB errors)
                application.log.error("Failed to create session", e) // Log error
                call.respond(HttpStatusCode.InternalServerError, "Failed to create session: ${e.message}")
            }
        }

        /**
         * GET /api/v1/sessions
         * Retrieves a list of all chat sessions (E2.S3 backend).
         * @return 200 OK with a list of ChatSessionSummary objects.
         * @throws Exception if retrieval fails.
         */
        get("/sessions") {
             try {
                val sessions = chatService.getAllSessionsSummaries()
                call.respond(HttpStatusCode.OK, sessions)
             } catch (e: Exception) {
                 application.log.error("Failed to get sessions list", e) // Log error
                 call.respond(HttpStatusCode.InternalServerError, "Failed to retrieve sessions: ${e.message}")
             }
        }

        // TODO: Implement routes for other endpoints as stories are picked up
        // Example: GET /api/v1/sessions/{id} (E2.S4 backend)
        get("/sessions/{id}") {
             val sessionId = call.parameters["id"]?.let { runCatching { UUID.fromString(it) }.getOrNull() }
                 ?: return@get call.respond(HttpStatusCode.BadRequest, "Invalid session ID format")

             try {
                 val session = chatService.getSessionDetails(sessionId)
                 if (session != null) {
                     call.respond(HttpStatusCode.OK, session)
                 } else {
                     call.respond(HttpStatusCode.NotFound, "Session not found")
                 }
             } catch (e: Exception) {
                  application.log.error("Failed to get session details for $sessionId", e) // Log error
                  call.respond(HttpStatusCode.InternalServerError, "Failed to retrieve session details: ${e.message}")
             }
        }


        // Example: POST /api/v1/sessions/{sessionId}/messages (E1.S4 backend)
        // post("/sessions/{sessionId}/messages") { ... }

        // Example: PUT /api/v1/messages/{id} (E3.S3 backend)
        // put("/messages/{id}") { ... }

        // ... other routes for models, settings, etc. ...
    }
}

// app/api/server/Serialization.kt (Ktor Feature Configuration)
package com.your_app_package.app.api.server

import io.ktor.serialization.kotlinx.json.*
import io.ktor.server.application.*
import io.ktor.server.plugins.contentnegotiation.*
import kotlinx.serialization.json.Json

/**
 * Configures the Content Negotiation feature for Ktor, enabling JSON serialization/deserialization.
 * Implements part of E7.S3.
 */
fun Application.configureSerialization() {
    install(ContentNegotiation) {
        json(Json {
            prettyPrint = true
            isLenient = true // Be lenient with JSON parsing
            ignoreUnknownKeys = true // Ignore extra fields in JSON
        })
    }
}
```

**6. DI Module and Application Entry Point (`app/`)**

Sets up Dependency Injection and the main application structure, including starting the server and database.

```kotlin
// app/App.kt
package com.your_app_package.app

import androidx.compose.ui.window.Window
import androidx.compose.ui.window.application
import com.your_app_package.app.api.client.ChatApi // Needs client interface
import com.your_app_package.app.api.client.KtorChatApiClient // Needs client impl
import com.your_app_package.app.api.server.apiRoutes // Needs server routes
import com.your_app_package.app.api.server.configureSerialization // Needs server serialization config
import com.your_app_package.app.data.dao.MessageDao // DAO interfaces
import com.your_app_package.app.data.dao.ModelDao
import com.your_app_package.app.data.dao.SessionDao // Used in DI
import com.your_app_package.app.data.dao.SettingsDao
import com.your_app_package.app.data.sqlite.Database // Needs DB setup object
import com.your_app_package.app.data.sqlite.MessageDaoExposed // DAO implementations
import com.your_app_package.app.data.sqlite.ModelDaoExposed
import com.your_app_package.app.data.sqlite.SessionDaoExposed // Used in DI
import com.your_app_package.app.data.sqlite.SettingsDaoExposed
import com.your_app_package.app.external.llm.LLMApiClient // External interfaces
import com.your_app_package.app.external.llm.LLMApiClientKtor // External implementations
import com.your_app_package.app.external.security.CredentialManager // External interfaces
import com.your_app_package.app.external.security.windows.WinCredentialManager // External implementations (E5.S1 spike result)
import com.your_app_package.app.service.ChatService // Service interfaces
import com.your_app_package.app.service.ChatServiceImpl // Service implementations (Used in DI)
import com.your_app_package.app.service.ModelService // Service interfaces
import com.your_app_package.app.service.ModelServiceImpl // Service implementations (Used in DI)
import com.your_app_package.app.ui.AppLayout // Needs main UI Composable
import com.your_app_package.app.ui.state.ChatState // Needs UI State holder (Used in DI)
import io.ktor.client.* // Needs Ktor Client
import io.ktor.client.engine.cio.* // Ktor Client engine
import io.ktor.server.application.* // Needs Ktor Application
import io.ktor.server.engine.* // Needs Ktor embedded server engine
import io.ktor.server.netty.* // Needs Netty engine for Ktor server
import io.ktor.server.routing.* // Needs Ktor routing feature
import org.koin.core.context.startKoin // Koin DI framework
import org.koin.dsl.module // Koin DI DSL
import org.koin.java.KoinJavaComponent.get // Helper to get from Koin in non-Composable parts
import java.io.File // Needed for DB path

// Define the Koin dependency injection module
val appModule = module {
    // --- Database (E7.S4) ---
    // Provide the Database connection manager
    single { Database } // Manages connection and schema creation

    // Provide DAO implementations using Exposed, inject Database if needed by DAO constructors
    // (Note: Exposed DAOs often manage transactions themselves and just need access to the connection,
    // which 'transaction {}' block handles if Database.connect is called first)
    single<SessionDao> { SessionDaoExposed() }
    single<MessageDao> { MessageDaoExposed() } // TODO: Implementation needed
    single<ModelDao> { ModelDaoExposed() } // TODO: Implementation needed
    single<SettingsDao> { SettingsDaoExposed() } // TODO: Implementation needed

    // --- External Services ---
    // Provide Ktor HttpClient for external LLM calls
    single {
        HttpClient(CIO) { // CIO engine suitable for desktop
            // TODO: Add Ktor client configuration (timeouts, logging, JSON via ContentNegotiation - E1.S4 needs JSON)
            install(io.ktor.client.plugins.contentnegotiation.ContentNegotiation) {
                json(kotlinx.serialization.json.Json { ignoreUnknownKeys = true })
            }
        }
    }
    // Provide LLM API Client implementation
    single<LLMApiClient> { LLMApiClientKtor(get()) } // Inject HttpClient (TODO: Implementation needed)

    // Provide Credential Manager implementation (E5.S1 result)
    single<CredentialManager> { WinCredentialManager() } // TODO: Implementation needed for Windows

    // --- Application/Service Layer ---
    // Provide Service implementations, injecting DAOs and External Clients
    single<ChatService> { ChatServiceImpl(get(), get(), get(), get(), get()) } // Inject all DAOs and LLMApiClient (TODO: Needs correct injection based on impl needs)
    single<ModelService> { ModelServiceImpl(get(), get(), get()) } // Inject ModelDao, SettingsDao, CredentialManager (TODO: Needs correct injection based on impl needs)

    // --- Frontend API Client Layer ---
    // Provide the Frontend API Client implementation, injecting Ktor HttpClient configured for localhost
    single<ChatApi> { KtorChatApiClient(get<HttpClient>()) } // KtorChatApiClient uses the same HttpClient but configured for localhost (needs specific setup or separate instance?)
    // *Correction:* KtorClient should be configured *once* for external calls.
    // The KtorChatApiClient for *localhost* communication might need a separate, simpler HttpClient instance
    // configured specifically for http://localhost, or careful base URL handling in the client.
    // For simplicity in DI setup, let's assume one HttpClient and base URL handling for now.
    // Or better, have two HttpClient factories/qualifiers if needed: one for external, one for local.
    // Let's assume one for now, base URL handled in client impls.

    // --- UI Layer ---
    // Provide the UI State Holder, injecting the Frontend API Client
    single { ChatState(get<ChatApi>()) } // ChatState depends on ChatApi
}


/**
 * Application entry point for the Compose Desktop Chatbot.
 * Sets up DI, Database, Embedded Ktor Server, and the main application window.
 * Implements E7.S2, E7.S3, E7.S4, E7.S7 setup.
 */
fun main() = application {
    // 1. Setup Koin Dependency Injection
    startKoin {
        modules(appModule)
        // Optional: Add logger if needed
        // printLogger(Level.DEBUG)
    }

    // 2. Get core components from DI for setup
    val database: Database = get()
    val chatService: ChatService = get() // Get services needed by Ktor routes
    val modelService: ModelService = get()

    // 3. Initialize SQLite Database and Schema (E7.S4)
    // Use a file path in the user's application data directory
    val dbFile = File(System.getProperty("user.home"), ".mychatbot/chat.db")
    dbFile.parentFile.mkdirs() // Ensure directory exists
    database.connect("jdbc:sqlite:${dbFile.absolutePath}", driver = "org.sqlite.JDBC")

    // 4. Initialize Embedded Ktor Server (E7.S3)
    // Start on localhost, fixed port for V1 simplicity
    val serverPort = 8080
    val server = embeddedServer(Netty, port = serverPort) {
        // Configure Ktor features
        configureSerialization() // Configure JSON (part of E7.S3)

        // Configure Ktor routing, injecting necessary services
        routing {
            apiRoutes(chatService, modelService) // Call the routes definition function, inject services
        }
        // Optional: Add logging, error handling features
        // install(StatusPages) { ... }
        // install(CallLogging) { ... }
    }
    // Start the server without blocking the main UI thread
    server.start(wait = false)

    // 5. Setup Main Application Window (E7.S2)
    Window(onCloseRequest = {
        // 6. Handle Graceful Shutdown (E7.S7)
        server.stop(1000, 5000) // Stop Ktor server gracefully (wait 1s for jobs, 5s for connections)
        database.close() // Close database connection
        // TODO: Close Ktor HTTP Client instances from DI if they manage persistent connections
        // Koin will stop when application exits, but explicit close might be needed for resources
        exitApplication() // Exit the Compose application
    }, title = "AIChat Desktop App") {
        // Get the UI State holder from DI and pass it to the main layout Composable
        val chatState: ChatState = get()
        AppLayout(chatState) // Pass state to the main UI layout
    }
}

// app/data/sqlite/Database.kt (Minimal helper for connection)
package com.your_app_package.app.data.sqlite

import com.your_app_package.app.data.models.ChatMessages
import com.your_app_package.app.data.models.ChatSessions
import com.your_app_package.app.data.models.LLMModels
import com.your_app_package.app.data.models.ModelSettings
import org.jetbrains.exposed.sql.Database as ExposedDatabase // Alias to avoid name collision
import org.jetbrains.exposed.sql.SchemaUtils
import org.jetbrains.exposed.sql.transactions.transaction

/**
 * Manages the SQLite database connection and schema initialization using Exposed.
 * Implements E7.S4 database setup.
 */
object Database {
    private var db: ExposedDatabase? = null

    /**
     * Establishes the database connection and creates the schema if it doesn't exist.
     * @param url The JDBC connection URL for the SQLite database file.
     * @param driver The JDBC driver class name (e.g., "org.sqlite.JDBC").
     */
    fun connect(url: String, driver: String) {
        if (db == null) {
            db = ExposedDatabase.connect(url, driver = driver)
            // Run schema creation within a transaction
            transaction {
                SchemaUtils.create(ChatSessions, ChatMessages, LLMModels, ModelSettings)
                // TODO: Add initial data if needed (e.g., a default model/settings)
                // Example:
                // if (LLMModels.selectAll().empty()) {
                //     val defaultModelId = LLMModels.insert { ... } get LLMModels.id
                //     ModelSettings.insert { it[modelId] = defaultModelId; ... }
                // }
            }
        }
    }

    /**
     * Closes the database connection.
     * Part of graceful shutdown (E7.S7).
     */
    fun close() {
        db?.let {
            // There isn't a standard Exposed way to explicitly close the underlying JDBC connection pool.
            // Exposed relies on the connection pool manager (HikariCP if used explicitly, or default).
            // For a simple embedded app, sometimes stopping the server implicitly closes connections.
            // If using a pool, need to get the underlying DataSource and close it.
            // With the basic JDBC driver, the connection is typically managed by the transaction block.
            // A safer approach for explicit close might involve configuring Exposed with HikariCP
            // and closing the HikariDataSource.
            // For V1 minimal, relying on OS process termination to cleanup resources might suffice,
            // but adding HikariCP for robust management is a good idea for production.
            println("Database close called. Ensure connection pool is configured for graceful shutdown.")
        }
        db = null
    }

    // Could add a property to get the Exposed Database instance if needed elsewhere for manual transactions
    // val exposedDatabase: ExposedDatabase get() = db ?: error("Database not connected")
}

// app/api/client/KtorChatApiClient.kt (Needed in DI setup, minimal skeleton for now)
package com.your_app_package.app.api.client

import com.your_app_package.app.shared.models.*
import io.ktor.client.HttpClient
import io.ktor.client.call.body
import io.ktor.client.request.*
import io.ktor.http.*
import java.util.UUID
import kotlinx.serialization.Serializable

// Matches backend DTO
@Serializable
data class CreateSessionRequest(val name: String?)

/**
 * Implementation of the ChatApi interface using Ktor HttpClient to communicate with the embedded Ktor server.
 * Consumed by the UI State holder (ChatState).
 */
class KtorChatApiClient(private val httpClient: HttpClient) : ChatApi {

    // TODO: Get port dynamically from server startup if not fixed
    private val baseUrl = "http://localhost:8080/api/v1" // Fixed port for now (E7.S3)

    /**
     * @inheritDoc
     * Implements frontend client logic for POST /api/v1/sessions (E2.S1 frontend).
     */
    override suspend fun createSession(name: String?): ChatSession {
        // Suspend function calling Ktor HttpClient suspend functions
        return httpClient.post("$baseUrl/sessions") {
            contentType(ContentType.Application.Json)
            if (name != null) {
                setBody(CreateSessionRequest(name)) // Ktor serializes DTO to JSON
            }
        }.body() // Ktor deserializes response JSON to ChatSession
    }

    /**
     * @inheritDoc
     * Implements frontend client logic for GET /api/v1/sessions (E2.S3 frontend).
     */
    override suspend fun getSessions(): List<ChatSessionSummary> {
        return httpClient.get("$baseUrl/sessions").body() // Ktor deserializes response JSON to List<ChatSessionSummary>
    }

    // TODO: Implement other ChatApi methods as stories are picked up
    override suspend fun getSession(id: UUID): ChatSession { TODO("Not yet implemented for E2.S4") }
    override suspend fun updateSession(id: UUID, name: String?, modelId: UUID?, settingsId: UUID?): ChatSession { TODO("Not yet implemented for E2.S5, E4.S7") }
    override suspend fun deleteSession(id: UUID) { TODO("Not yet implemented for E2.S6") }
    override suspend fun assignSessionToGroup(id: UUID, groupId: UUID?): ChatSessionSummary { TODO("Not yet implemented for E6.S1") }
    override suspend fun sendMessage(sessionId: UUID, content: String): List<ChatMessage> { TODO("Not yet implemented for E1.S4") }
    override suspend fun updateMessage(id: UUID, content: String): ChatMessage { TODO("Not yet implemented for E3.S3") }
    override suspend fun deleteMessage(id: UUID) { TODO("Not yet implemented for E3.S4") }
    override suspend fun getModels(): List<LLMModel> { TODO("Not yet implemented for E4.S2") }
    override suspend fun addModel(name: String, baseUrl: String, type: String, apiKey: String?): LLMModel { TODO("Not yet implemented for E4.S1") }
    override suspend fun updateModel(id: UUID, name: String?, baseUrl: String?, apiKey: String?, type: String?): LLMModel { TODO("Not yet implemented for E4.S3") }
    override suspend fun deleteModel(id: UUID) { TODO("Not yet implemented for E4.S4") }
    override suspend fun getSettings(id: UUID): ModelSettings? { TODO("Not yet implemented for E4.S6") }
    override suspend fun getAllSettings(): List<ModelSettings> { TODO("Not yet implemented for E4.S5 UI") }
    override suspend fun addSettings(modelId: UUID, name: String, systemMessage: String?, temperature: Float?, maxTokens: Int?, customParamsJson: String?): ModelSettings { TODO("Not yet implemented for E4.S5") }
    override suspend fun updateSettings(id: UUID, name: String?, systemMessage: String?, temperature: Float?, maxTokens: Int?, customParamsJson: String?): ModelSettings { TODO("Not yet implemented for E4.S6") }
    override suspend fun deleteSettings(id: UUID) { TODO("Not yet implemented for E4.S5") }
    override suspend fun isApiKeyConfiguredForModel(modelId: UUID): Boolean { TODO("Not yet implemented for E5.S4") }
}

// Dummy/Placeholder implementations for services/DAOs not in scope of E2.S1 backend
// app/data/sqlite/MessageDaoExposed.kt (Placeholder)
package com.your_app_package.app.data.sqlite
import com.your_app_package.app.data.dao.MessageDao
import com.your_app_package.app.shared.models.ChatMessage
import java.util.UUID
class MessageDaoExposed : MessageDao {
    override fun getMessagesBySessionId(sessionId: UUID): List<ChatMessage> { TODO("Not yet implemented for E2.S4") }
    override fun insertMessage(message: ChatMessage): UUID { TODO("Not yet implemented for E1.S4") }
    override fun updateMessage(id: UUID, content: String) { TODO("Not yet implemented for E3.S3") }
    override fun deleteMessage(id: UUID) { TODO("Not yet implemented for E3.S4") }
    override fun getNextSequenceNumber(sessionId: UUID): Int { TODO("Not yet implemented for E1.S4") }
}
// app/data/sqlite/ModelDaoExposed.kt (Placeholder)
package com.your_app_package.app.data.sqlite
import com.your_app_package.app.data.dao.ModelDao
import com.your_app_package.app.shared.models.LLMModel
import java.util.UUID
class ModelDaoExposed : ModelDao {
    override fun getAllModels(): List<LLMModel> { TODO("Not yet implemented for E4.S2") }
    override fun getModelById(id: UUID): LLMModel? { TODO("Not yet implemented for E1.S4, E4.S3") }
    override fun getModelByApiKeyId(apiKeyId: String): LLMModel? { TODO("Not yet implemented for E5.S3") }
    override fun insertModel(model: LLMModel): UUID { TODO("Not yet implemented for E4.S1") }
    override fun updateModel(id: UUID, name: String?, baseUrl: String?, apiKeyId: String?, type: String?) { TODO("Not yet implemented for E4.S3") }
    override fun deleteModel(id: UUID) { TODO("Not yet implemented for E4.S4") }
}
// app/data/sqlite/SettingsDaoExposed.kt (Placeholder)
package com.your_app_package.app.data.sqlite
import com.your_app_package.app.data.dao.SettingsDao
import com.your_app_package.app.shared.models.ModelSettings
import java.util.UUID
class SettingsDaoExposed : SettingsDao {
    override fun getSettingsById(id: UUID): ModelSettings? { TODO("Not yet implemented for E1.S4, E4.S6") }
    override fun getSettingsByModelId(modelId: UUID): List<ModelSettings> { TODO("Not yet implemented for E4.S5 UI") }
    override fun insertSettings(settings: ModelSettings): UUID { TODO("Not yet implemented for E4.S5") }
    override fun updateSettings(id: UUID, name: String?, systemMessage: String?, temperature: Float?, maxTokens: Int?, customParamsJson: String?) { TODO("Not yet implemented for E4.S6") }
    override fun deleteSettings(id: UUID) { TODO("Not yet implemented for E4.S5") }
}
// app/external/llm/LLMApiClientKtor.kt (Placeholder)
package com.your_app_package.app.external.llm
import com.your_app_package.app.external.models.OpenAiApiModels.*
import com.your_app_package.app.shared.models.ChatMessage
import com.your_app_package.app.shared.models.LLMModel
import com.your_app_package.app.shared.models.ModelSettings
import io.ktor.client.HttpClient
import java.util.UUID
class LLMApiClientKtor(private val httpClient: HttpClient) : LLMApiClient {
    override suspend fun completeChat(messages: List<ChatMessage>, modelConfig: LLMModel, settings: ModelSettings, apiKey: String): ChatCompletionResponse { TODO("Not yet implemented for E1.S4") }
}
// app/external/security/windows/WinCredentialManager.kt (Placeholder)
package com.your_app_package.app.external.security.windows
import com.your_app_package.app.external.security.CredentialManager
class WinCredentialManager : CredentialManager { // TODO: Needs implementation for E5.S1
    override fun storeCredential(alias: String, credential: String): String? { TODO("Not yet implemented") }
    override fun getCredential(alias: String): String? { TODO("Not yet implemented") }
    override fun deleteCredential(alias: String): Boolean { TODO("Not yet implemented") }
}
```

---

This code covers:

*   **Database Setup (E7.S4):** The `Database` object for connecting and the Exposed `UUIDTable` definitions with necessary columns and foreign keys (`ChatSessions`, `ChatMessages`, `LLMModels`, `ModelSettings`).
*   **DAO Implementation (E2.S1 backend, parts of E2.S3, E2.S4, E2.S6, E6.S1):** `SessionDaoExposed` with the `insertSession`, `getAllSessions`, `getSessionById`, `updateSession`, and `deleteSession` methods, using `transaction(Dispatchers.IO)`.
*   **Service Implementation (E2.S1 backend, parts of E2.S3, E2.S4, E2.S5, E2.S6, E6.S1):** `ChatServiceImpl` with the `createSession`, `getAllSessionsSummaries`, `getSessionDetails`, `updateSessionDetails`, `deleteSession`, `assignSessionToGroup` methods, calling the `SessionDao`. Placeholder service and DAO implementations are included for DI wiring.
*   **Ktor Routes (E2.S1 backend, E7.S3, parts of E2.S3, E2.S4):** `ApiRoutes.kt` with the `post("/sessions")` and `get("/sessions")` route handlers, calling the `ChatService`, and the basic `/status` route. It also includes the `CreateSessionRequest` DTO.
*   **Application Entry Point (`App.kt`):** Demonstrates how Koin DI is started, the `Database` is connected, the Ktor `embeddedServer` is started with serialization and the defined routes, and the main Compose `Window` is launched. It also includes basic shutdown logic (E7.S7).
*   **Frontend API Client Interface (`ChatApi`) and Ktor Implementation (`KtorChatApiClient`) (parts of E2.S1 frontend, E2.S3 frontend):** Included as a placeholder and dependency in `App.kt` and DI, with the `createSession` and `getSessions` methods defined for the frontend to call.

This provides the backend structure needed for E2.S1 and lays the groundwork for E2.S3, E2.S4, E2.S6, E6.S1, and integrating the larger E1.S4. The KDoc explains the purpose and links back to the user stories.


