# Code Review
- We use the package name "eu.torvian.chatbot" instead of "com.your_app_package.app".
- In data classes, instead of java.util.UUID we use String for IDs. (In implementation classes we can still use UUIDs from Java.)
- We don't use UUIDTable from Exposed, but LongIdTable and store the IDs as longs.


- We don't need a separate class for this. We can pass a string to the API call directly.
```kotlin
/**
* Data Transfer Object for the request body of the POST /api/v1/sessions endpoint (E2.S1).
* Contains the optional name for the new session.
  */
  @Serializable
  data class CreateSessionRequest(
  val name: String?
  )
```

