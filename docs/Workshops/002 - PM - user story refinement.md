Okay team, based on our workshop discussion and the valuable input everyone provided, I've refined the user story descriptions and acceptance criteria. I've incorporated the clarifications on scope, dependencies, and added notes where certain tasks (like persistence, architectural patterns, coroutines) are cross-cutting requirements for other stories rather than standalone items.

I'll also include the initial S/M/L estimates we discussed as a starting point for our sprint planning session.

Here are the finalized user stories:

---

### Epic 1: Core Chat Interaction & Experience

*   **E1.S1 - Send a User Message (Estimate: S)**
  *   **Description:** As a user, I want to type text into the message input field and trigger sending it, so that I can initiate a conversation with the LLM.
  *   **Acceptance Criteria:**
    *   A multi-line text input field is present in the main chat view.
    *   A "Send" button is visible and actionable next to the input field.
    *   Pressing the Enter key while in the input field (or clicking the "Send" button) triggers the message sending process.
    *   The text content of the input field is cleared immediately after sending is triggered.
    *   The "Send" action (button/Enter key) is disabled if the input field is empty or contains only whitespace.
*   **E1.S2 - Display Sent User Message (Estimate: S)**
  *   **Description:** As a user, I want my message to appear in the chat history area immediately after I send it, so that I can see my contribution to the conversation without delay.
  *   **Acceptance Criteria:**
    *   A visual representation of the user's message (content, role, temporary ID/timestamp) is added to the end of the chat history display immediately after the send action is triggered.
    *   The message is clearly styled or positioned to indicate it is from the "user".
    *   The message bubble/item includes the message text.
    *   **(Cross-cutting Requirement):** Ensure this user message is persisted to the database as part of this action.
*   **E1.S3 - Show LLM Response Loading State (Estimate: S)**
  *   **Description:** As a user, I want to see a visual indicator while the application is waiting for the LLM's response, so that I know my request is being processed.
  *   **Acceptance Criteria:**
    *   A loading indicator (e.g., a progress bar, spinner) becomes visible in or near the chat area after a user message is sent and before the assistant message is received.
    *   The loading indicator disappears once an assistant message is received or an error related to the LLM call is displayed.
*   **E1.S4 - Get LLM Response and Display Assistant Message (Estimate: L)**
  *   **Description:** As a user, I want the application to send my message to the configured LLM, receive its response, and display it in the chat history, so I can continue the conversation.
  *   **Acceptance Criteria:**
    *   Upon triggering the send action (E1.S1), the backend collects necessary context: the user message content, relevant previous messages from the session history (applying a basic token window strategy for V1), and the selected model/settings for the session.
    *   The backend makes an HTTP request to the selected LLM's OpenAI-compatible API endpoint using the Ktor client.
    *   **(Dependency):** The backend retrieves the API key securely using the mechanism from **E5.S2**.
    *   The backend successfully receives and parses the LLM's chat completion response.
    *   The extracted assistant message content is saved to the database, associated with the current session and including the model/settings used.
    *   The newly saved assistant message is added to the chat history display in the UI.
    *   The assistant message is clearly styled or positioned to indicate it is from the "assistant".
*   **E1.S5 - Maintain Message Order in Session (Estimate: S)**
  *   **Description:** As a user, I want all messages within a session to be displayed in the correct chronological order, so that I can easily follow the flow of the conversation.
  *   **Acceptance Criteria:**
    *   Messages loaded from the database for a session are queried and displayed ordered by their `sequence` number or `createdAt` timestamp.
    *   Newly added user and assistant messages appear at the bottom of the message list.
*   **E1.S6 - Handle Basic LLM API Errors and Display to User (Estimate: M)**
  *   **Description:** As a user, I want to be clearly notified if the application encounters an error when trying to get a response from the LLM, so that I understand why the assistant message didn't appear.
  *   **Acceptance Criteria:**
    *   If the Ktor client call (in E1.S4) fails (e.g., network error, non-success HTTP status code from API like 401, 429, 500), the error is caught by the backend service.
    *   A user-friendly error message (e.g., "Error contacting LLM: [Brief reason]") is communicated to the frontend state.
    *   The UI displays this error message to the user (e.g., temporary notification, error bar).
    *   The application does not crash due to these API errors.
    *   The loading indicator (E1.S3) is dismissed when the error is displayed.

---

### Epic 2: Robust Chat Session Management

*   **E2.S1 - Create and Auto-Select New Chat Session (Estimate: S)**
  *   **Description:** As a user, I want to click a button to start a brand new, empty conversation thread, so that I can begin a fresh topic.
  *   **Acceptance Criteria:**
    *   A button or action to "Create New Session" is present in the session list UI.
    *   Clicking this action triggers the backend to create a new `ChatSession` record in the database with default values (unique ID, current timestamp, default name like "New Session").
    *   The newly created session appears in the session list.
    *   The new session is automatically selected and becomes the active session, clearing the main chat area.
    *   **(Cross-cutting Requirement):** The new session record is persisted in the database upon creation.
*   **E2.S2 - Data Persistence (Cross-cutting Requirement)**
  *   **Description:** As a user, I want all changes to my chat sessions, messages, models, and settings to be automatically saved to the database as I make them, so that my data is retained between application uses and is safe from unexpected closures.
  *   **Acceptance Criteria:** This is not a single user story but a required outcome of several other stories. Specifically, persistence must be ensured for:
    *   Creation of new sessions (E2.S1).
    *   Saving user messages (E1.S2).
    *   Saving assistant messages (E1.S4).
    *   Renaming sessions (E2.S5).
    *   Deleting sessions (E2.S6).
    *   Saving message edits (E3.S3).
    *   Deleting messages (E3.S4).
    *   Adding/Updating/Deleting LLM models (E4.S1, E4.S3, E4.S4).
    *   Adding/Updating/Deleting Model Settings (E4.S5, E4.S6).
    *   Saving API key references (E5.S1).
    *   Assigning sessions to groups (E6.S1).
*   **E2.S3 - Load and Display Session List on Startup (Estimate: M)**
  *   **Description:** As a user, when I open the application, I want to see a list of all my previously saved chat sessions, so that I can choose which conversation to resume.
  *   **Accept Criteria:**
    *   Upon application launch, the application queries the database for all `ChatSession` records.
    *   A dedicated UI panel or area displays the list of sessions.
    *   Each list item shows the session's name and potentially the group it belongs to (from E6.S2).
    *   The list is ordered logically (e.g., by `updatedAt` descending).
*   **E2.S4 - Select Session and Load Messages (Estimate: M)**
  *   **Description:** As a user, I want to click on an item in the session list to make it the active conversation and load all its messages, so that I can continue chatting in that specific context.
  *   **Acceptance Criteria:**
    *   Clicking a session item in the list updates the UI state to mark that session as selected.
    *   The application queries the database for all `ChatMessage` records associated with the selected session ID.
    *   The messages are loaded and displayed in the main chat area, ordered chronologically (E1.S5).
    *   **(Dependency):** Database indexing for `ChatMessage.sessionId` and `ChatMessage.sequence` (from E7.S4) is in place to ensure reasonable load time for sessions with up to 100-200 messages.
*   **E2.S5 - Rename a Chat Session (Estimate: S)**
  *   **Description:** As a user, I want to be able to change the name of a chat session after it's created, so that I can give it a more descriptive title based on the conversation content.
  *   **Acceptance Criteria:**
    *   A UI mechanism (e.g., context menu option "Rename", clickable session title) allows initiating the rename action for a session in the list.
    *   An input field or editor appears, pre-filled with the current session name.
    *   Saving the new name updates the `name` field of the `ChatSession` record in the database.
    *   The session list UI updates to show the new name.
    *   **(Cross-cutting Requirement):** The renaming action is persisted to the database.
*   **E2.S6 - Delete a Chat Session with Confirmation (Estimate: M)**
  *   **Description:** As a user, I want to remove a chat session and all its associated messages, so that I can clean up unwanted conversations from my list.
  *   **Acceptance Criteria:**
    *   A UI mechanism (e.g., context menu option "Delete") allows initiating the delete action for a session.
    *   Clicking "Delete" displays a confirmation dialog asking the user to confirm the action and warning that messages will be lost.
    *   Confirming the deletion removes the `ChatSession` record from the database.
    *   **(Dependency):** Associated `ChatMessage` records for that session are automatically deleted from the database due to the foreign key constraint with `ON DELETE CASCADE` configured on the `ChatMessage.sessionId` column (as part of E7.S4).
    *   The deleted session is removed from the session list UI.
    *   If the deleted session was the currently active one, the UI navigates to an empty state or selects a default session (e.g., the most recent one).
*   **E2.S7 - Copy Entire Session Content to Clipboard (Estimate: M)**
  *   **Description:** As a user, I want to copy the full text content of the current chat session to my system clipboard, so that I can easily paste or share the entire conversation transcript.
  *   **Acceptance Criteria:**
    *   A UI action (e.g., button in the chat area header, menu item) is available to trigger copying the current session.
    *   Clicking this action retrieves all messages for the current session from the backend/database.
    *   The frontend logic formats the retrieved messages into a single text string, including clear indicators of speaker turns (e.g., "User: ", "Assistant: ", or markdown formatting if markdown rendering is implemented).
    *   The formatted text string is placed onto the system clipboard using Compose Desktop's `ClipboardManager`.
    *   A brief visual confirmation (e.g., a "Copied!" tooltip) is shown to the user.

---

### Epic 3: Advanced Message Control

*   **E3.S1 - Initiate Editing User Message (Estimate: M)**
  *   **Description:** As a user, I want to click on a user message to start editing its content, so I can correct mistakes or refine my prompts.
  *   **Acceptance Criteria:**
    *   Each user message item in the chat history has an accessible "Edit" action (e.g., an icon visible on hover or in a context menu).
    *   Clicking the "Edit" action changes the display of that specific message item.
    *   The message text area is replaced by an editable text input field pre-populated with the current message content.
    *   "Save" and "Cancel" actions become visible within or near the editing message item.
*   **E3.S2 - Initiate Editing Assistant Message (Estimate: M)**
  *   **Description:** As a user, I want to click on an assistant message to start editing its content, so I can adjust AI responses to better suit my needs (e.g., fix formatting, shorten).
  *   **Acceptance Criteria:**
    *   Each assistant message item has an accessible "Edit" action (same mechanism as E3.S1).
    *   Clicking "Edit" changes the display of the message item to an editable state, same as E3.S1.
*   **E3.S3 - Save Edited Message Content (Estimate: M)**
  *   **Description:** As a user, when I finish editing a message, I want to save my changes, so that the updated message is permanently recorded.
  *   **Acceptance Criteria:**
    *   Clicking the "Save" action on an editing message item triggers an update action.
    *   The backend receives the message ID and the new text content.
    *   The backend updates the `content` field for the corresponding `ChatMessage` record in the database.
    *   The `updatedAt` timestamp for the message is updated in the database.
    *   The message item in the UI reverts from the editing state back to display mode, showing the newly saved content.
    *   **(Optional V1 Enhancement):** The message item could display a small visual indicator (e.g., "(edited)") next to the timestamp.
    *   **(Cross-cutting Requirement):** The updated message record is persisted in the database.
*   **E3.S4 - Delete a Specific Message (Estimate: M)**
  *   **Description:** As a user, I want to remove an individual message (user or assistant) from a chat session, so I can clean up the conversation history or remove irrelevant parts.
  *   **Acceptance Criteria:**
    *   Each message item has an accessible "Delete" action (e.g., an icon visible on hover or in a context menu).
    *   Clicking the "Delete" action displays a confirmation dialog.
    *   Confirming the deletion triggers a delete action for that message.
    *   The backend receives the message ID and deletes the corresponding `ChatMessage` record from the database.
    *   The message item is removed from the UI display for the session.
    *   **(Cross-cutting Requirement):** The deletion is persisted in the database.
*   **E3.S5 - Copy Single Message Content to Clipboard (Estimate: S)**
  *   **Description:** As a user, I want to copy the raw text content of a single message to my system clipboard, so I can easily use specific parts of a conversation elsewhere.
  *   **Acceptance Criteria:**
    *   Each message item has an accessible "Copy" action (e.g., an icon visible on hover or in a context menu).
    *   Clicking the "Copy" action retrieves the raw text content of that specific message.
    *   The message content is placed onto the system clipboard using Compose Desktop's `ClipboardManager`.
    *   A brief visual confirmation (e.g., a "Copied!" tooltip) is shown to the user.

---

### Epic 4: Comprehensive LLM & Settings Configuration

*   **E4.S1 - Add New LLM Model Configuration (Estimate: M)**
  *   **Description:** As a user, I want to define a new LLM endpoint connection by providing its name, base URL, and type, so the application can connect to it.
  *   **Acceptance Criteria:**
    *   A dedicated settings section or dialog is available for managing LLM models.
    *   An action to add a new model is present (e.g., a button).
    *   A form is displayed allowing input for Model Name (text), Base URL (text), and Type (e.g., dropdown with 'openai', 'openrouter').
    *   Saving the form creates a new `LLMModel` record in the database with a unique ID.
    *   The newly created model appears in the list of configured models.
    *   **(Cross-cutting Requirement):** The new model record is persisted in the database.
    *   **(Dependency):** API Key input and secure storage (E5.S1) are handled separately but are linked to this model configuration.
*   **E4.S2 - View Configured LLM Models (Estimate: M)**
  *   **Description:** As a user, I want to see a list of all the LLM connections I have configured, so I can manage my available models.
  *   **Acceptance Criteria:**
    *   The LLM Model management UI displays a list of all `LLMModel` records retrieved from the database.
    *   Each list item shows at least the model name.
    *   The list also indicates the API key status for each model (from E5.S4).
*   **E4.S3 - Update LLM Model Configuration (Estimate: M)**
  *   **Description:** As a user, I want to modify the name, base URL, or type of an existing LLM model configuration, so I can correct errors or update connection details.
  *   **Acceptance Criteria:**
    *   Selecting a model from the list (in E4.S2) displays its details in an editable form (Model Name, Base URL, Type).
    *   Saving changes updates the corresponding `LLMModel` record in the database.
    *   The model list (in E4.S2) updates to reflect the changes.
    *   **(Cross-cutting Requirement):** The updated model record is persisted.
    *   **(Dependency):** Updating the API Key is handled separately via **E5.S1**.
*   **E4.S4 - Delete LLM Model Configuration (Estimate: M)**
  *   **Description:** As a user, I want to remove an LLM model configuration I no longer plan to use, so I can keep my list of available models clean.
  *   **Acceptance Criteria:**
    *   A UI action to delete a model configuration is available (e.g., button on edit form, context menu).
    *   A confirmation dialog is displayed before deletion.
    *   Confirming deletion removes the `LLMModel` record from the database.
    *   **(Dependency):** All `ModelSettings` records associated with this model are also deleted (`ON DELETE CASCADE` or manually).
    *   **(Dependency):** The corresponding API key credential is securely deleted from the OS credential manager (**E5.S3**).
    *   Any `ChatSession` or `ChatMessage` records referencing the deleted model/settings have their foreign keys set to NULL or a default.
    *   The model disappears from the list in E4.S2.
    *   **(Cross-cutting Requirement):** The deletion is persisted.
*   **E4.S5 - Manage Model Settings Profiles (Estimate: M)**
  *   **Description:** As a user, for each configured LLM model, I want to create, name, and manage different sets of interaction settings (like "Default", "Creative", "Strict"), so I can easily apply preferred behaviors.
  *   **Acceptance Criteria:**
    *   Within the UI for a specific model configuration (E4.S2/E4.S3), there is a list displaying its associated settings profiles.
    *   Actions are available to add a new settings profile (providing a name), rename existing ones, and delete existing ones (with confirmation).
    *   `ModelSettings` records are created, updated, and deleted in the database, linked by `modelId`.
    *   **(Cross-cutting Requirement):** Changes to settings profiles (add/rename/delete) are persisted.
*   **E4.S6 - Edit Model Settings Parameters (Estimate: L)**
  *   **Description:** As a user, within a settings profile, I want to customize parameters like the system message, temperature, and other model-specific options, so I can fine-tune the LLM's responses for specific tasks.
  *   **Acceptance Criteria:**
    *   Selecting a settings profile (in E4.S5 list) displays an editable form for its parameters.
    *   Fields are available for:
      *   System Message (multi-line text)
      *   Temperature (numeric input, e.g., 0.0 to 2.0)
      *   Max Tokens (integer input)
      *   Top P (numeric input)
      *   Frequency Penalty (numeric input)
      *   Presence Penalty (numeric input)
    *   A mechanism is available to add/edit/delete other model-specific parameters, stored as JSON (`customParamsJson`), potentially a simple key-value pair editor for V1.
    *   Saving the form updates the corresponding `ModelSettings` record in the database.
    *   Input validation is performed for numeric parameters.
    *   **(Cross-cutting Requirement):** The updated settings record is persisted.
*   **E4.S7 - Select Model & Settings Profile for Session (Estimate: M)**
  *   **Description:** As a user, within an active chat session, I want to easily choose which configured model and specific settings profile will be used for the *next* assistant response, allowing me to change the AI's approach mid-conversation.
  *   **Acceptance Criteria:**
    *   A UI element (e.g., dropdown menu) is visible in the chat session view (near the input area) showing the currently selected model/settings name for this session.
    *   Clicking the element displays a list of configured models and their available settings profiles.
    *   Selecting a different model/settings profile updates the UI element to show the new selection.
    *   The `currentModelId` and `currentSettingsId` fields for the active `ChatSession` record in the database are updated to reflect the selection.
    *   **(Dependency):** The LLM calling logic (in E1.S4) reads these `currentModelId` and `currentSettingsId` from the session record to determine which model and settings to use for the next response.
    *   **(Cross-cutting Requirement):** The updated session record is persisted.

---

### Epic 5: Secure API Key Handling

*   **E5.S1 - Implement Secure API Key Storage via OS Credential Manager (Estimate: L)**
  *   **Description:** As a developer, I need to implement the core logic for securely storing and managing sensitive API keys outside the main database file using the operating system's credential management capabilities, so that user keys are protected locally.
  *   **Acceptance Criteria:**
    *   A backend service/module is created (`CredentialManager` or similar) that can interface with the Windows Credential Manager API using appropriate Kotlin/Java libraries or native calls.
    *   The `CredentialManager` can accept an API key string and an alias/ID, securely store the key, and return the reference ID used for storage.
    *   The `CredentialManager` can retrieve a securely stored key given its reference ID.
    *   The `CredentialManager` can delete a securely stored key given its reference ID.
    *   This storage method is integrated into the Model configuration saving flow (part of E4.S1, E4.S3). The `LLMModel` database table stores the reference ID, not the key.
    *   Sensitive keys are only held in application memory when actively needed for an API call or initial storage/deletion.
*   **E5.S2 - Securely Retrieve API Key for LLM API Calls (Estimate: S)**
  *   **Description:** As the application backend, I need to use the secure storage mechanism (E5.S1) to retrieve the actual API key just before making an external LLM API call, ensuring the key is not sitting in memory unnecessarily.
  *   **Acceptance Criteria:**
    *   The LLM communication logic (part of E1.S4) uses the `CredentialManager` (from E5.S1) to retrieve the API key using the stored reference ID from the `LLMModel` record.
    *   The retrieved, decrypted key is used in the HTTP request header (typically `Authorization: Bearer <key>`) via the Ktor client.
    *   The retrieved key is not logged or stored in the database.
*   **E5.S3 - Securely Delete API Key on Model Removal (Estimate: S)**
  *   **Description:** As the application backend, when an LLM model configuration is deleted, I need to ensure the associated API key is also removed from the secure OS credential manager, so that sensitive data is cleaned up.
  *   **Acceptance Criteria:**
    *   The model deletion logic (part of E4.S4) identifies the API key reference ID associated with the model being deleted.
    *   The backend uses the `CredentialManager` (from E5.S1) to delete the credential corresponding to that reference ID from the OS credential manager.
*   **E5.S4 - Indicate API Key Configuration Status in UI (Estimate: S)**
  *   **Description:** As a user, I want to see if an API key has been configured for a specific model in the settings UI without the key being displayed, so I know if that model is ready for use.
  *   **Acceptance Criteria:**
    *   The Model management UI (E4.S2) displays a status indicator for each model (e.g., text label like "Configured", "Not Set", "Error") based on whether a valid key reference exists and can be accessed via the `CredentialManager`.
    *   The actual API key string is never displayed after initial input.

---

### Epic 6: Chat Session Organization & Navigation

*   **E6.S1 - Assign Session to a Group (Estimate: M)**
  *   **Description:** As a user, I want to be able to categorize a chat session by assigning it a group name, so I can organize my conversations by topic or project.
  *   **Acceptance Criteria:**
    *   A UI element or mechanism (e.g., a field in a session info panel, context menu option) is available to assign or change the group name for the currently selected session.
    *   The backend supports storing a group identifier (`groupId`) for each `ChatSession` record. For V1, the group name string can be stored directly in this field, or a simple conceptual ID referencing the name. (No separate `groups` table for V1).
    *   User input for the group name updates the `groupId` field for the session.
    *   **(Cross-cutting Requirement):** The change is persisted to the database.
*   **E6.S2 - View Sessions Grouped in the List (Estimate: M)**
  *   **Description:** As a user, I want the main list of chat sessions to be organized visually by the group name I assigned, so I can easily browse my categorized conversations.
  *   **Acceptance Criteria:**
    *   The session list UI (E2.S3) displays sessions grouped together based on their assigned `groupId`.
    *   Sessions with the same `groupId` are listed under a common heading or section.
    *   Sessions without a `groupId` are listed in a separate "Ungrouped" section.
    *   The grouping is applied when the session list is initially loaded and updates as sessions are assigned to groups (E6.S1).

---

### Epic 7: Application Core Framework & Windows 11 Integration

*   **E7.S1 - Create Basic Windows 11 Installer (Estimate: M)**
  *   **Description:** As a potential user, I want to easily install the application on my Windows 11 computer, so I can start using it without complex manual setup.
  *   **Acceptance Criteria:**
    *   The Gradle build configuration includes a task (using Compose Desktop's packaging tools) to generate a Windows-specific installer package (e.g., `.exe`).
    *   Running the generated installer successfully installs the application files and creates shortcuts (Start Menu, Desktop - optional).
    *   The installed application launches correctly on a standard Windows 11 environment.
*   **E7.S2 - Initialize Main Application Window (Estimate: S)**
  *   **Description:** As a user, when I launch the application, a primary application window should appear, providing the container for all UI elements.
  *   **Acceptance Criteria:**
    *   The application's entry point (`main` function) uses Compose for Desktop's `application` and `Window` components to create the main application window.
    *   The window has a title (e.g., "AIChat Desktop App").
    *   The window provides a basic layout structure (e.g., using `Column`, `Row`) ready for integrating other UI components (session list, chat area, input area).
*   **E7.S3 - Initialize Embedded Ktor Server (Estimate: L)**
  *   **Description:** As a developer, I need the Ktor HTTP server to start automatically within the application process on launch, so the frontend can communicate with the backend logic via defined API endpoints.
  *   **Acceptance Criteria:**
    *   Code exists in the application startup sequence to configure and start an embedded Ktor server instance (e.g., using Netty engine).
    *   The server binds to a `localhost` address on a configured or dynamically found available port.
    *   Basic Ktor features are configured (e.g., Content Negotiation for JSON).
    *   Initial routing structure (`/api/v1/...`) is defined.
    *   The server starts successfully without errors reported in logs.
*   **E7.S4 - Initialize SQLite Database & Schema (Estimate: L)**
  *   **Description:** As a developer, I need the application to connect to the SQLite database file on startup and ensure all necessary tables and structures are created or updated, so that data persistence works reliably.
  *   **Acceptance Criteria:**
    *   Database connection logic (using SQLDelight or Exposed) is implemented to connect to a SQLite file.
    *   The database file is created in a user-specific, platform-appropriate location (e.g., `user.home/.mychatbot/chat.db`) if it doesn't exist.
    *   Database tables (`chat_sessions`, `chat_messages`, `llm_models`, `model_settings`) are defined in the database schema.
    *   Table creation scripts or migration logic runs on startup to create tables if they are missing.
    *   Foreign key constraints are defined, notably `ChatMessage.sessionId` referencing `ChatSession.id` with `ON DELETE CASCADE`.
    *   Indices are defined for frequently queried columns, especially foreign keys (`ChatMessage.sessionId`, `ModelSettings.modelId`, `ChatSession.groupId`) and `ChatMessage.sequence`.
    *   The database connection is established and ready for use by backend DAOs/services.
*   **E7.S5 - Implement Layered Architecture (Cross-cutting Requirement)**
  *   **Description:** As a developer, I will structure the codebase with clear boundaries between the UI, Application Logic, Data Access, and External Services layers, ensuring dependencies flow correctly and interfaces are used to allow for potential future extraction or modularity.
  *   **Acceptance Criteria:** This is not a single user story but a coding standard and architectural task applied *during* the implementation of other stories.
    *   Code is organized into logical modules/packages corresponding to the layers (e.g., `ui`, `app.service`, `data.sqlite`, `external.llm`).
    *   Core business logic resides in the service layer, not directly in the UI or data layer.
    *   Data access logic is encapsulated within a dedicated layer (e.g., using DAOs) that abstracts SQLite specifics from the service layer.
    *   External service communication (LLM calls) is abstracted behind interfaces/classes used by the service layer.
    *   Dependencies are primarily unidirectional (UI -> Service -> Data/External).
    *   Code reviews enforce adherence to this architecture.
*   **E7.S6 - Use Coroutines for Asynchronous Operations (Cross-cutting Requirement)**
  *   **Description:** As a developer, I will ensure that all I/O-bound and potentially blocking operations (database access, network calls) are performed asynchronously using Kotlin Coroutines, so that the application UI remains responsive and the application feels fluid.
  *   **Acceptance Criteria:** This is a coding standard applied *during* the implementation of other stories.
    *   All database read/write operations are performed within appropriate coroutine contexts (e.g., `Dispatchers.IO`).
    *   All Ktor client calls are correctly handled as suspend functions within coroutines.
    *   Long-running background tasks (like waiting for an LLM response) do not block the main UI thread or Ktor's event loop.
    *   State updates in the UI triggered by asynchronous results are handled correctly to ensure thread safety and proper recomposition.
    *   Code reviews enforce the consistent use of coroutines for asynchronous work.
*   **E7.S7 - Graceful Application Shutdown (Estimate: M)**
  *   **Description:** As the application, I need to shut down cleanly when the user closes the window or the operating system requests termination, ensuring all resources are released and data integrity is maintained.
  *   **Acceptance Criteria:**
    *   Clicking the main application window's close button initiates a shutdown sequence.
    *   The SQLite database connection is properly closed.
    *   The embedded Ktor server instance is stopped cleanly.
    *   Any other significant resources (like the Ktor HttpClient instance) are closed.
    *   The application process exits without errors or leaving zombie processes.

---

These stories, along with their refined acceptance criteria and initial estimates, should provide a clear and actionable backlog for the development team to begin sprint planning and execution. Let me know if any further clarification is needed as we move forward!